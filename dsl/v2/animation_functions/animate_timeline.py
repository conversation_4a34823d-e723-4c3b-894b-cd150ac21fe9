"""
effect: |
  在Manim场景中创建并播放一个动态的、分段构建的时间轴动画。
  每个事件包含时间点（年份）、标题、描述文本，并可选择性地包含图片和自定义颜色。
  事件详情会交替显示在时间轴的上方和下方。

use_cases:
  - 展示项目里程碑或历史事件序列，具有更强的视觉吸引力
  - 解释一个过程的各个阶段，每个阶段有清晰的标题和描述
  - 可视化产品发布路线图，突出显示关键节点

params:
  scene:
    type: FeynmanScene
    desc: Manim场景实例（由系统自动传入）
  events:
    type: list[EventData | dict[str, Any]]
    desc: 时间轴事件列表。每个元素可以是EventData对象或字典，包含year(时间点), title(标题), description(描述), emoji(表情), color(颜色), narration(事件旁白)等属性，其中narration会在当前时间轴事件显示的同时作为旁白播放。如果有全局的content_narration，那么事件的narration将被忽略。
    required: true
  intro_narration:
    type: str
    desc: 时间轴动画开始时播放的语音旁白文本（会配合开始标题的显示动画一起播放）
    default: None
  outro_narration:
    type: str
    desc: 时间轴动画结束时播放的语音旁白文本（会配合最后整个时间轴缩放动画一起播放）
    default: None
  content_narration:
    type: str
    desc: 时间轴动画播放时同步播放的语音旁白文本（与每个事件的narration互斥）
    required: true
  title:
    type: str
    desc: 时间轴标题
    default: None
  subtitle:
    type: str
    desc: 时间轴副标题
    default: None
  id:
    type: str
    desc: 创建的Manim Mobject的唯一标识符
    default: None

dsl_examples:
  - type: animate_timeline
    params:
      events:
        - year: "1950"
          title: "The Beginning"
          description: "A new era starts."
          emoji: "🌍"
          color: "#F0E68C"
          narration: "A new era starts."
        - year: "1965"
          title: "Major Discovery"
          description: "Key findings published."
          emoji: "🌞"
          color: "#FFA500"
          narration: "Key findings published."
        - year: "1980"
          title: "Expansion"
          description: "Growth and development."
          emoji: "🌛"
          color: "#B22222"
          narration: "Growth and development."
        - year: "2000"
          title: "New Century"
          description: "Looking ahead."
          emoji: "🪵"
          color: "#FFC0CB"
          narration: "Looking ahead."
        - year: "2020"
          title: "Modern Times"
          description: "Current state of affairs."
          emoji: "🚀"
          color: "#9370DB"
          narration: "Current state of affairs."
      title: "Historical Timeline"
      intro_narration: "A journey through time, highlighting key moments."
      outro_narration: "This is the end of the timeline."

notes:
  - 事件列表按数组中的顺序呈现，而非按年份排序
  - 时间轴展示时会先显示主轴，然后顺序显示每个事件
  - 可以为每个事件指定颜色，或使用默认颜色方案
  - emoji属性会尝试下载并显示对应图标，如果失败则仅显示简单节点
  - 如果时间轴节点较多（超过5个），每个节点单独的narration会使视频变得冗长，可以设置简洁的整体content_narration来介绍时间轴的背景信息
"""
import os
from dataclasses import dataclass
from typing import TYPE_CHECKING, Any, Optional

import requests
from loguru import logger
from manim import *

from dsl.v2.themes.theme_utils import ThemeUtils
from utils.format import wrap_text

if TYPE_CHECKING:
    from dsl.v2.core.scene import FeynmanScene


@dataclass
class EventData:
    """事件数据类"""

    year: str
    title: str
    description: str
    emoji: str
    color: str
    narration: Optional[str] = None


@dataclass
class EventElement:
    """Timeline event element class"""

    group: Group
    node: Circle
    emoji: Optional[ImageMobject]
    year: VGroup
    title: Text
    desc: Text
    connector: Line
    color: str


def download_emoji_image(emoji: str) -> Optional[str]:
    """下载emoji图片

    Args:
        emoji: 要下载的emoji字符

    Returns:
        保存的文件路径，如果下载失败则返回None
    """
    emoji_dir = "emoji_cache"
    if not os.path.exists(emoji_dir):
        os.makedirs(emoji_dir)

    # 生成emoji编码
    if len(emoji) == 0:
        return None

    emoji_code = hex(ord(emoji[0]))[2:].lower()
    emoji_name = f"emoji_{emoji_code}"
    filepath = os.path.join(emoji_dir, f"{emoji_name}.png")

    if os.path.exists(filepath):
        return filepath

    try:
        url = f"https://raw.githubusercontent.com/twitter/twemoji/master/assets/72x72/{emoji_code}.png"
        response = requests.get(
            url,
            timeout=5,
        )
        if response.status_code == 200:
            with open(filepath, "wb") as f:
                f.write(response.content)
            return filepath
    except Exception as e:
        logger.error(f"下载emoji失败: {e}")

    return None


def create_emoji_mobject(emoji: str) -> Optional[ImageMobject]:
    """创建emoji的Mobject

    Args:
        emoji: 要创建的emoji字符

    Returns:
        emoji图片对象，如果创建失败则返回None
    """
    try:
        image_path = download_emoji_image(emoji)

        if image_path and os.path.exists(image_path):
            emoji_mob = ImageMobject(image_path)
            return emoji_mob
    except Exception as e:
        logger.error(f"无法加载emoji图片: {e}")

    return None


def create_event_element(
    event_data: EventData, colors: dict[str, str], node_radius: float = 0.6, node_spacing: float = 6.0
) -> EventElement:
    """创建单个事件元素

    Args:
        event_data: 事件数据
        colors: 颜色配置字典
        node_radius: 节点半径
        node_spacing: 节点间距

    Returns:
        创建好的事件元素对象
    """
    node_radius = ThemeUtils.get_component_style("timeline", "node_radius", node_radius)
    # 如果事件没有指定颜色，则使用连接线颜色作为默认值
    event_color = ManimColor(event_data.color or colors.get("timeline", "#4B90E2"))

    # 创建节点
    node = Circle(radius=node_radius, color=event_color)
    node.set_fill(event_color, opacity=1)
    stroke_width = ThemeUtils.get_component_style("timeline", "node_stroke_width", 5)
    # 使用简化结构，不采用特殊的node_stroke字段
    stroke_color = ThemeUtils.get_color("background", DARK_GRAY)  # 使用背景色作为节点描边颜色
    node.set_stroke(stroke_color, width=stroke_width)

    # 创建emoji图标（如果有）
    emoji_icon = None
    if hasattr(event_data, "emoji") and event_data.emoji:
        emoji_icon = create_emoji_mobject(event_data.emoji)
        if emoji_icon:
            emoji_icon.move_to(node.get_center())
            emoji_icon.scale_to_fit_width(node.width * 0.5)

    # 年份标签
    year_bg_width = ThemeUtils.get_component_style("timeline", "year_bg_width", 1.6)
    year_bg_height = ThemeUtils.get_component_style("timeline", "year_bg_height", 0.6)
    year_corner_radius = ThemeUtils.get_component_style("timeline", "year_corner_radius", 0.3)
    year_bg = RoundedRectangle(
        width=year_bg_width, height=year_bg_height, corner_radius=year_corner_radius, color=event_color
    )
    year_bg.set_fill(event_color, opacity=1)

    year_font_size = ThemeUtils.get_font_size("small", 20)  # 使用基本小号文本大小
    year_font = ThemeUtils.get_font("timeline_year", "Georgia")  # 年份标签保留特殊字体
    year_text_color = WHITE  # 年份标签使用白色以确保在颜色块上可见
    year_text = Text(event_data.year, font_size=year_font_size, weight=BOLD, color=year_text_color, font=year_font)
    year_text.move_to(year_bg.get_center())

    year_text_scale_factor = ThemeUtils.get_component_style("timeline", "year_text_scale_factor", 0.7)
    if year_text.width > year_bg.width * year_text_scale_factor:
        year_text.scale_to_fit_width(year_bg.width * year_text_scale_factor)
    year_group = VGroup(year_bg, year_text)

    # 标题
    title_str = event_data.title or ""
    title_font_size = ThemeUtils.get_font_size("body", 24)  # 使用基本正文大小
    title_font = ThemeUtils.get_font("primary", "Microsoft YaHei")  # 使用主要字体
    title_color = colors["text_primary"]  # 使用全局颜色字典中的主要文本颜色
    wrapped_title_str = wrap_text(title_str, 16)
    title_text = Text(
        "\n".join(wrapped_title_str),
        font_size=title_font_size,
        weight=BOLD,
        color=title_color,
        font=title_font,
    )

    # 描述
    desc_str = event_data.description or ""
    desc_font_size = ThemeUtils.get_font_size("small", 20)  # 使用基本小号文本大小
    desc_font = ThemeUtils.get_font("primary", "Microsoft YaHei")  # 使用主要字体
    desc_color = colors["text_secondary"]  # 使用全局颜色字典中的次要文本颜色
    wrapped_desc = wrap_text(desc_str, max_length=20)
    desc_text = Text(
        "\n".join(wrapped_desc),
        font_size=desc_font_size,
        weight=NORMAL,
        color=desc_color,
        font=desc_font,
    )
    desc_scale_factor = ThemeUtils.get_component_style("timeline", "desc_scale_factor", 0.45)
    if desc_text.width > node_spacing * desc_scale_factor:
        desc_text.scale_to_fit_width(node_spacing * desc_scale_factor)

    # 布局
    year_spacing = ThemeUtils.get_spacing("timeline_year", 0.7)
    title_spacing = ThemeUtils.get_spacing("timeline_title", 0.3)
    desc_spacing = ThemeUtils.get_spacing("timeline_description", 0.25)

    year_group.next_to(node, UP, buff=year_spacing)
    title_text.next_to(year_group, UP, buff=title_spacing)
    desc_text.next_to(title_text, UP, buff=desc_spacing)

    # 连接线
    connector_stroke_width = ThemeUtils.get_component_style("timeline", "connector_stroke_width", 4)
    # 获取全局颜色字典中的timeline颜色（即timeline_connector_color）
    connector_color = colors["timeline"] if colors else event_color  # 如果没有全局颜色，则使用事件颜色
    connector = Line(
        node.get_top(),
        year_group.get_bottom(),
        stroke_width=connector_stroke_width,
        color=connector_color,
    )

    # 组合所有元素
    element_group = Group(node, year_group, title_text, desc_text, connector)
    if emoji_icon:
        element_group.add(emoji_icon)

    return EventElement(
        group=element_group,
        node=node,
        emoji=emoji_icon,
        year=year_group,
        title=title_text,
        desc=desc_text,
        connector=connector,
        color=str(event_color),
    )


def _normalize_events(events: list[EventData | dict[str, Any]]) -> list[EventData]:
    """将事件数据标准化为EventData对象

    Args:
        events: 原始事件数据列表

    Returns:
        标准化后的EventData对象列表
    """
    normalized_events: list[EventData] = []
    # 使用主题定义的事件颜色列表，如果没有则使用默认颜色列表
    default_colors = ThemeUtils.get_cyclic_colors(len(events))  # 使用主题中定义的颜色列表

    for i, event_data in enumerate(events):
        if isinstance(event_data, dict):
            normalized_events.append(
                EventData(
                    year=event_data.get("year", ""),
                    title=event_data.get("title", ""),
                    description=event_data.get("description", ""),
                    emoji=event_data.get("emoji", ""),
                    color=event_data.get("color", "") or default_colors[i % len(default_colors)],
                    narration=event_data.get("narration"),
                )
            )
        elif isinstance(event_data, EventData):
            if event_data.color is None:
                event_data.color = default_colors[i % len(default_colors)]
            normalized_events.append(event_data)
        else:
            logger.warning(f"跳过无效事件数据: {event_data}")

    return normalized_events


def _create_title_group(title: Optional[str], subtitle: Optional[str], text_color: str) -> Optional[VGroup]:
    """创建标题组

    Args:
        title: 主标题文本
        subtitle: 副标题文本
        text_color: 文本主色调

    Returns:
        标题组VGroup对象
    """
    if not title:
        return None

    # 使用主题化的标题字体大小、字体和颜色
    title_font_size = ThemeUtils.get_font_size("timeline_main_title", 52)  # 保留主标题专用大小
    title_font = ThemeUtils.get_font("primary", "Arial")  # 使用主要字体
    title_color = ThemeUtils.get_color("text_primary", text_color)  # 复用主要文本颜色

    main_title = Text(
        title,
        font_size=title_font_size,
        weight=BOLD,
        color=title_color,
        font=title_font,
    )

    if subtitle:
        # 使用主题化的副标题字体大小、字体和颜色
        subtitle_font_size = ThemeUtils.get_font_size("body", 24)  # 使用基本正文大小
        subtitle_font = ThemeUtils.get_font("primary", "Arial")  # 使用主要字体
        subtitle_color = ThemeUtils.get_color("text_secondary", text_color)  # 复用次要文本颜色
        subtitle_text = Text(subtitle, font_size=subtitle_font_size, color=subtitle_color, font=subtitle_font)

        title_spacing = ThemeUtils.get_spacing("timeline_title_subtitle", 0.3)
        title_group = VGroup(main_title, subtitle_text)
        title_group.arrange(DOWN, buff=title_spacing)
    else:
        title_group = VGroup(main_title)

    title_edge_buff = ThemeUtils.get_spacing("timeline_title_edge", 0.5)
    title_group.to_edge(UP, buff=title_edge_buff)
    return title_group


def _layout_timeline_elements(elements: list[EventElement], title_group: Optional[VGroup]) -> None:
    """布局时间轴元素

    Args:
        elements: 时间轴事件元素列表
        title_group: 标题组对象
    """
    if not title_group or not elements:
        return

    # 找出最高的元素
    tallest_element = max(elements, key=lambda e: e.group.height)

    # 计算可用空间并进行缩放
    available_height = title_group.get_bottom()[1] - (-config.frame_height / 2)
    usable_height = available_height - MED_SMALL_BUFF

    scale_factor = 1.0
    if tallest_element.group.height > usable_height:
        scale_factor = usable_height / tallest_element.group.height
        tallest_element.group.scale(scale_factor)

    # 定位最高元素
    tallest_element.group.next_to(title_group, DOWN, buff=MED_SMALL_BUFF)
    timeline_y = tallest_element.node.get_center()[1]

    # 对齐其他元素
    for element in elements:
        if element != tallest_element:
            if scale_factor != 1.0:
                element.group.scale(scale_factor)

            current_node_y = element.node.get_center()[1]
            move_distance = timeline_y - current_node_y
            element.group.shift([0, move_distance, 0])


def _animate_timeline_elements(
    scene: "FeynmanScene",
    elements: list[EventElement],
    normalized_events: list[EventData],
    title_group: Optional[VGroup],
    node_spacing: float,
    intro_narration: Optional[str],
    outro_narration: Optional[str],
    content_narration: Optional[str],
) -> Group:
    """创建并播放时间轴动画

    Args:
        scene: 场景对象
        elements: 时间轴事件元素列表
        title_group: 标题组对象
        node_spacing: 节点间距
        intro_narration: 开场旁白文本
        outro_narration: 结尾旁白文本
        content_narration: 时间轴背景介绍文本
    Returns:
        包含所有时间轴元素和标题的组合对象
    """
    timeline_elements: list[EventElement] = []
    timeline_segments: list[Line] = []

    def _play_title_animation():
        if title_group:
            title_animation_duration = ThemeUtils.get_animation_duration("timeline_title", 2)
            title_shift_amount = ThemeUtils.get_component_style("timeline", "title_shift_amount", 0.2)
            title_lag_ratio = ThemeUtils.get_component_style("timeline", "title_lag_ratio", 0.3)
            title_wait_time = ThemeUtils.get_animation_duration("timeline_title_wait", 0.5)

            scene.play(
                LaggedStart(
                    Write(title_group[0]),
                    FadeIn(title_group[1], shift=UP * title_shift_amount) if len(title_group) > 1 else Wait(0),
                    lag_ratio=title_lag_ratio,
                ),
                run_time=title_animation_duration,
            )
            scene.wait(title_wait_time)

    if intro_narration:
        with scene.voiceover(intro_narration) as tracker:  # noqa
            _play_title_animation()
    else:
        _play_title_animation()

    def _play_event_animation(total_duration: Optional[float] = None):
        # Default proportions for sub-animations within an event if total_duration is used.
        default_proportions = {
            "node_group": 0.3,  # For AnimationGroup(*node_animations)
            "info_lagged": 0.4, # For LaggedStart for info display
            "wiggle": 0.1,      # For Wiggle animation of emoji
            "final_wait": 0.2   # For scene.wait at the end of event animation
        }
        MIN_ANIM_DURATION = 0.05  # Minimum perceptible duration for an animation segment

        # These themed durations are defined here to be available in _animate_node's closure
        # and are used as fallbacks if specific override durations are not provided or applicable.
        info_animation_duration = ThemeUtils.get_animation_duration("timeline_info", 0.7)
        element_wait_time = ThemeUtils.get_animation_duration("timeline_element_wait", 0.3)
        # Note: Default run_times for AnimationGroup and Wiggle are often 1s in Manim if not specified.

        def _animate_node(
            node_group_dur: Optional[float] = None,
            info_lagged_dur: Optional[float] = None,
            wiggle_dur: Optional[float] = None,
            final_wait_dur: Optional[float] = None
        ):
            # Determine run_time for node_animations group
            rt_node_group = node_group_dur
            if rt_node_group is not None and rt_node_group <= MIN_ANIM_DURATION:
                rt_node_group = 0  # Make it instant if calculated duration is too small
            # If rt_node_group is None, Manim's AnimationGroup default run_time (usually 1s) applies.
            scene.play(AnimationGroup(*node_animations, lag_ratio=0.5), run_time=rt_node_group)

            # Determine run_time for info_lagged animation
            rt_info_lagged = info_lagged_dur
            if rt_info_lagged is None: # Fallback to themed default if no override
                rt_info_lagged = info_animation_duration
            elif rt_info_lagged <= MIN_ANIM_DURATION:
                rt_info_lagged = 0 # Make it instant

            scene.play(
                LaggedStart(
                    Create(element.connector),
                    FadeIn(element.year, scale=info_year_scale),
                    Write(element.title),
                    FadeIn(element.desc, shift=DOWN * info_desc_shift),
                    lag_ratio=info_lag_ratio, # This lag_ratio is themed
                ),
                run_time=rt_info_lagged,
            )

            if element.emoji:
                rt_wiggle = wiggle_dur
                if rt_wiggle is not None and rt_wiggle <= MIN_ANIM_DURATION:
                    rt_wiggle = 0 # Make it instant
                # If rt_wiggle is None, Manim's Wiggle default run_time (usually 1s) applies.
                scene.play(Wiggle(element.emoji, scale_value=1.5, rotation_angle=0.1 * TAU), run_time=rt_wiggle)

            actual_wait_time = final_wait_dur
            if actual_wait_time is None: # Fallback to themed default
                actual_wait_time = element_wait_time
            elif actual_wait_time <= MIN_ANIM_DURATION:
                actual_wait_time = 0 # No wait

            if actual_wait_time > 0:
                scene.wait(actual_wait_time)

        # 事件动画 (Main loop for event animations)
        for i, element in enumerate(elements):
            # 移动已有元素 (Move existing elements)
            if i > 0:
                animations = []
                for elem in timeline_elements:
                    animations.append(elem.group.animate.shift(LEFT * node_spacing / 2))
                for seg in timeline_segments:
                    animations.append(seg.animate.shift(LEFT * node_spacing / 2))
                if animations:
                    scene.play(*animations)

            # 创建连接线 (Create connecting line)
            segment = None
            if i > 0:
                rightmost_node = timeline_elements[-1].node
                start_pos = rightmost_node.get_center() + RIGHT * 0.6
                end_pos = element.node.get_center() + LEFT * 0.6
                segment = Line(start_pos, end_pos, stroke_width=8, color=element.color)
                timeline_segments.append(segment)

            # 节点入场动画 (Node entrance animation setup)
            node_emoji_scale = ThemeUtils.get_component_style("timeline", "emoji_scale_factor", 0.6)
            node_animations = [
                GrowFromCenter(element.node),
                (FadeIn(element.emoji, scale=node_emoji_scale) if element.emoji else Wait(0.01)), # Use small Wait if no emoji for AnimationGroup structure
            ]
            if segment:
                node_animations.insert(0, Create(segment))

            current_event_data = normalized_events[i]
            # Themed styles for info display, used in _animate_node
            info_year_scale = ThemeUtils.get_component_style("timeline", "year_scale_factor", 0.8)
            info_desc_shift = ThemeUtils.get_component_style("timeline", "desc_shift_amount", 0.2)
            info_lag_ratio = ThemeUtils.get_component_style("timeline", "info_lag_ratio", 0.2)

            # --- Duration override calculation logic ---
            node_group_dur_override = None
            info_lagged_dur_override = None
            wiggle_dur_override = None
            final_wait_dur_override = None

            if total_duration is not None and elements and len(elements) > 0:
                event_segment_duration = total_duration / len(elements)

                current_proportions = default_proportions.copy()
                if not element.emoji: # If no emoji, redistribute its proportion
                    if "wiggle" in current_proportions and current_proportions["wiggle"] > 0:
                        # Simple redistribution: add to final_wait; ensure final_wait exists
                        current_proportions["final_wait"] = current_proportions.get("final_wait", 0) + current_proportions.pop("wiggle")

                total_prop_weight = sum(current_proportions.values())
                if total_prop_weight > 0: # Avoid division by zero
                    node_group_dur_override = (event_segment_duration * current_proportions.get("node_group", 0)) / total_prop_weight
                    info_lagged_dur_override = (event_segment_duration * current_proportions.get("info_lagged", 0)) / total_prop_weight
                    if element.emoji and "wiggle" in current_proportions:
                         wiggle_dur_override = (event_segment_duration * current_proportions["wiggle"]) / total_prop_weight
                    final_wait_dur_override = (event_segment_duration * current_proportions.get("final_wait", 0)) / total_prop_weight
            # --- End of duration override calculation ---

            narration_to_play = None
            # Prioritize detailed_description as per memory, then fallback to .narration
            if hasattr(current_event_data, 'detailed_description') and current_event_data.detailed_description:
                narration_to_play = current_event_data.detailed_description
            elif hasattr(current_event_data, 'narration') and current_event_data.narration:
                narration_to_play = current_event_data.narration

            if total_duration is None and narration_to_play:
                with scene.voiceover(narration_to_play) as tracker:  # noqa
                    # Voiceover controls timing; _animate_node uses its defaults by passing None
                    _animate_node(None, None, None, None)
            else:
                # No event-specific narration for this event, or use_event_narration is False.
                # Use calculated overrides if available, otherwise _animate_node uses defaults.
                _animate_node(
                    node_group_dur_override,
                    info_lagged_dur_override,
                    wiggle_dur_override,
                    final_wait_dur_override
                )
            timeline_elements.append(element)

    if content_narration:
        with scene.voiceover(content_narration) as tracker:  # noqa
            _play_event_animation(tracker.duration)
    else:
        _play_event_animation()

    def _play_final_animation():
        # 最终调整
        final_wait_time = ThemeUtils.get_animation_duration("timeline_final_wait", 1)
        scene.wait(final_wait_time)

        # 创建完整的时间轴组
        all_timeline = Group()
        for elem in timeline_elements:
            all_timeline.add(elem.group)
        for seg in timeline_segments:
            all_timeline.add(seg)

        # 宽度调整
        current_width = all_timeline.width
        screen_width_factor = ThemeUtils.get_component_style("timeline", "screen_width_factor", 0.95)
        screen_width = config.frame_width * screen_width_factor

        scale_factor = 1
        if current_width > screen_width:
            scale_factor = screen_width / current_width

        final_animation_duration = ThemeUtils.get_animation_duration("timeline_final_adjustment", 1.5)
        final_spacing = ThemeUtils.get_spacing("timeline_final", LARGE_BUFF)

        scene.play(
            all_timeline.animate.scale(scale_factor).next_to(title_group, DOWN, buff=final_spacing)
            if title_group
            else all_timeline.animate.scale(scale_factor).center(),
            run_time=final_animation_duration,
        )
        end_wait_time = ThemeUtils.get_animation_duration("timeline_end_wait", 0.5)
        scene.wait(end_wait_time)

        # 组合所有元素
        if title_group:
            all_elements_group = Group(all_timeline, title_group)
        else:
            all_elements_group = all_timeline

        return all_elements_group

    if outro_narration:
        with scene.voiceover(outro_narration) as tracker:  # noqa
            _play_final_animation()
    else:
        _play_final_animation()


def animate_timeline(
    scene: "FeynmanScene",
    events: list[EventData | dict[str, Any]],
    intro_narration: Optional[str] = None,
    outro_narration: Optional[str] = None,
    content_narration: Optional[str] = None,
    title: Optional[str] = None,
    subtitle: Optional[str] = None,
    id: Optional[str] = None,
) -> None:
    logger.info(f"Animating dynamic timeline with {len(events)} events in region 'full_screen'...")
    scene.clear_current_mobj()
    unique_id = id or f"timeline_dynamic_{abs(hash(str(events))) % 10000}"

    # 检查事件是否为空
    if not events:
        logger.warning("Timeline events is empty. Nothing to show.")
        return None

    # 使用主题化的颜色配置
    colors = {
        "bg": ThemeUtils.get_color("timeline_background", "#F7F6F1"),
        "text_primary": ThemeUtils.get_color("text_primary", "#333333"),  # 复用基本文本颜色
        "text_secondary": ThemeUtils.get_color("text_secondary", "#666666"),  # 复用次要文本颜色
        "timeline": ThemeUtils.get_color("timeline_connector_color", "#4B90E2"),  # 新的连接线颜色
    }

    # 设置参数 - 使用主题化的尺寸
    node_spacing = ThemeUtils.get_component_style("timeline", "node_spacing", 6.0)
    node_radius = ThemeUtils.get_component_style("timeline", "node_radius", 0.6)

    # 标准化事件数据为EventData对象
    normalized_events = _normalize_events(events)
    if not normalized_events:
        logger.warning("没有事件数据可以动画展示.")
        return

    target_rect = scene.full_screen_rect
    if not target_rect:
        logger.error("目标区域 'full_screen' 未找到. 无法放置时间轴.")
        return

    # 创建标题（如果提供）
    title_group = _create_title_group(title, subtitle, colors["text_primary"])

    # 使用改进的时间轴样式
    # 创建所有事件元素
    event_elements = []
    for event_data in normalized_events:
        event_elements.append(create_event_element(event_data, colors, node_radius, node_spacing))

    # 调整位置和间距
    _layout_timeline_elements(event_elements, title_group)

    # 动画展示时间轴
    result_group = _animate_timeline_elements(
        scene,
        event_elements,
        normalized_events,
        title_group,
        node_spacing,
        intro_narration,
        outro_narration,
        content_narration,
    )

    # 保存结果并更新当前场景中的对象
    if id:
        scene.save_mobject(result_group, unique_id)

    scene.current_mobj = result_group
    logger.info(f"Timeline '{unique_id}' animation complete.")
    return None
